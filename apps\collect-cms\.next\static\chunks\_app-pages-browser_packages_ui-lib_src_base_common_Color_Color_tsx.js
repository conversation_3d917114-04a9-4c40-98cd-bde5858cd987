/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_packages_ui-lib_src_base_common_Color_Color_tsx"],{

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/Color/color.module.scss":
/*!*********************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Color/color.module.scss ***!
  \*********************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"wrapper\":\"color_wrapper__yuBaS\",\"row\":\"color_row__afjAf\",\"bg__color\":\"color_bg__color__UyYaA\",\"tag\":\"color_tag__OFAjQ\",\"description\":\"color_description__pExio\"};\n    if(true) {\n      // 1748453773073\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  \nmodule.exports.__checksum = \"a328b1763f3d\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL0NvbG9yL2NvbG9yLm1vZHVsZS5zY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0Esa0JBQWtCO0FBQ2xCLE9BQU8sSUFBVTtBQUNqQjtBQUNBLHNCQUFzQixtQkFBTyxDQUFDLHdNQUFtSixjQUFjLHNEQUFzRDtBQUNyUCxNQUFNLFVBQVU7QUFDaEI7QUFDQTtBQUNBO0FBQ0EseUJBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL0NvbG9yL2NvbG9yLm1vZHVsZS5zY3NzPzdmN2QiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZXh0cmFjdGVkIGJ5IG1pbmktY3NzLWV4dHJhY3QtcGx1Z2luXG5tb2R1bGUuZXhwb3J0cyA9IHtcIndyYXBwZXJcIjpcImNvbG9yX3dyYXBwZXJfX3l1QmFTXCIsXCJyb3dcIjpcImNvbG9yX3Jvd19fYWZqQWZcIixcImJnX19jb2xvclwiOlwiY29sb3JfYmdfX2NvbG9yX19VeVlhQVwiLFwidGFnXCI6XCJjb2xvcl90YWdfX09GQWpRXCIsXCJkZXNjcmlwdGlvblwiOlwiY29sb3JfZGVzY3JpcHRpb25fX3BFeGlvXCJ9O1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIC8vIDE3NDg0NTM3NzMwNzNcbiAgICAgIHZhciBjc3NSZWxvYWQgPSByZXF1aXJlKFwiRDovQ0RBL3JlcG9zL2JyYW5kLWNvbXBhc3MtZnJvbnRlbmQtdGVtcGxhdGUvYXBwcy9jb2xsZWN0LWNtcy9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL21pbmktY3NzLWV4dHJhY3QtcGx1Z2luL2htci9ob3RNb2R1bGVSZXBsYWNlbWVudC5qc1wiKShtb2R1bGUuaWQsIHtcInB1YmxpY1BhdGhcIjpcIi9fbmV4dC9cIixcImVzTW9kdWxlXCI6ZmFsc2UsXCJsb2NhbHNcIjp0cnVlfSk7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoY3NzUmVsb2FkKTtcbiAgICAgIFxuICAgIH1cbiAgXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJhMzI4YjE3NjNmM2RcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/Color/color.module.scss\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/Color/Color.tsx":
/*!*************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Color/Color.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Color: function() { return /* binding */ Color; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_debounce_collective_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,debounce!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/utils/throttle-debounce.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_debounce_collective_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,debounce!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _color_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./color.module.scss */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Color/color.module.scss\");\n/* harmony import */ var _color_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_color_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ Color auto */ \nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\nvar Color = function(param) {\n    var Colors = param.Colors;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: (_color_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"aidigi__grid\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_color_module_scss__WEBPACK_IMPORTED_MODULE_2___default().row),\n                style: {\n                    \"--count\": Colors.length\n                },\n                children: Colors === null || Colors === void 0 ? void 0 : Colors.map(function(color, idx) {\n                    var _color_AdditionalField;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_color_module_scss__WEBPACK_IMPORTED_MODULE_2___default().column),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BgColor, {\n                                color: color.HexColor\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 8\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: (_color_module_scss__WEBPACK_IMPORTED_MODULE_2___default().description),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"caption\", {\n                                        children: color.ColorName\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 9\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        children: \"hex:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                        lineNumber: 37,\n                                                        columnNumber: 11\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        children: color.HexColor\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                        lineNumber: 38,\n                                                        columnNumber: 11\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 10\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        children: \"rgb:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                        lineNumber: 41,\n                                                        columnNumber: 11\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        children: hexToRgb(color.HexColor)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                        lineNumber: 42,\n                                                        columnNumber: 11\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 10\n                                            }, _this),\n                                            color === null || color === void 0 ? void 0 : (_color_AdditionalField = color.AdditionalField) === null || _color_AdditionalField === void 0 ? void 0 : _color_AdditionalField.map(function(field, fieldIdx) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: [\n                                                                field.FieldName,\n                                                                \":\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                            lineNumber: 46,\n                                                            columnNumber: 12\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            children: field.FieldValue\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                            lineNumber: 47,\n                                                            columnNumber: 12\n                                                        }, _this)\n                                                    ]\n                                                }, fieldIdx, true, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 11\n                                                }, _this);\n                                            })\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 9\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 8\n                            }, _this)\n                        ]\n                    }, idx, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 7\n                    }, _this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                lineNumber: 21,\n                columnNumber: 4\n            }, _this)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n            lineNumber: 20,\n            columnNumber: 3\n        }, _this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n        lineNumber: 19,\n        columnNumber: 2\n    }, _this);\n};\n_c = Color;\nvar hexToRgb = function(hex) {\n    // Remove the \"#\" if present\n    hex = hex.replace(/^#/, \"\");\n    // Parse the hex values\n    var r, g, b;\n    if (hex.length === 3) {\n        // Short format (#RGB)\n        r = parseInt(hex[0] + hex[0], 16);\n        g = parseInt(hex[1] + hex[1], 16);\n        b = parseInt(hex[2] + hex[2], 16);\n    } else if (hex.length === 6) {\n        // Full format (#RRGGBB)\n        r = parseInt(hex.substring(0, 2), 16);\n        g = parseInt(hex.substring(2, 4), 16);\n        b = parseInt(hex.substring(4, 6), 16);\n    } else {\n        throw new Error(\"Invalid HEX color.\");\n    }\n    return \"\".concat(r, \", \").concat(g, \", \").concat(b);\n};\nvar BgColor = function(param) {\n    var color = param.color;\n    _s();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_3__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isCopied = _useState[0], setIsCopied = _useState[1];\n    var lazyCopy = (0,_barrel_optimize_names_Icon_debounce_collective_core__WEBPACK_IMPORTED_MODULE_4__.debounce)(function() {\n        setIsCopied(false);\n    }, 250);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onKeyDown: function() {\n            navigator.clipboard.writeText(color);\n            lazyCopy.clear();\n            setIsCopied(true);\n            lazyCopy();\n        },\n        tabIndex: 0,\n        role: \"button\",\n        className: (_color_module_scss__WEBPACK_IMPORTED_MODULE_2___default().bg__color),\n        style: {\n            background: color\n        },\n        onClick: function() {\n            navigator.clipboard.writeText(color);\n            lazyCopy.clear();\n            setIsCopied(true);\n            lazyCopy();\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_color_module_scss__WEBPACK_IMPORTED_MODULE_2___default().tag),\n            children: isCopied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_debounce_collective_core__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                        variant: \"copy\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 7\n                    }, _this),\n                    \" Copied\"\n                ]\n            }, void 0, true) : color\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n            lineNumber: 108,\n            columnNumber: 4\n        }, _this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Color\\\\Color.tsx\",\n        lineNumber: 90,\n        columnNumber: 3\n    }, _this);\n};\n_s(BgColor, \"dIAMLjsduWkZ4KaA+ylUo0FLqmM=\");\n_c1 = BgColor;\nvar _c, _c1;\n$RefreshReg$(_c, \"Color\");\n$RefreshReg$(_c1, \"BgColor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/Color/Color.tsx\n"));

/***/ })

}]);