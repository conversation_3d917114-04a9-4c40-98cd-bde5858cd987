@import '@/styles/config';

.wrapper {
	display: grid;
	grid-row: auto;
	position: relative;
}

.trigger__sidebar {
	--size: #{spacing(s6)};
}

.main {
	background-color: color('white', 0);
	display: grid;
	grid-template-columns: auto 1fr auto;
	gap: spacing(s8);

	.sidebar {
		background-color: color('white', 5);
		position: sticky;
		top: var(--toolbar-height);
		height: calc(100vh - var(--toolbar-height));
		width: px-to(320px, rem);
		// padding-top: spacing(s5);
		// padding-bottom: spacing(s5);
		transition: 0.2s width;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		z-index: 11;

		&.is__hidden {
			width: 0;
		}

		:global(.collect__button) {
			margin: spacing(s6) spacing(s6) 0;
		}

		&__layer {
			top: calc(var(--toolbar-height) + spacing(s4));
			border-radius: spacing(s2);
			border: px-to(1px, rem) solid color('grey', 15);
			height: calc(100vh - var(--toolbar-height) - spacing(s8));
			position: fixed;
			box-shadow: 0 spacing(s1) spacing(s2) rgba(69, 69, 69, 0.04);
			padding: spacing(s6) 0;

			&.left {
				left: calc(px-to(320px, rem) + spacing(s4));
				right: auto;
			}
			&.right {
				left: auto;
				right: calc(px-to(320px, rem) + spacing(s4));
			}
		}
	}
}

.header {
	box-shadow: 0px spacing(s1) spacing(s6) 0px rgba(69, 69, 69, 0.15);
	position: sticky;
	width: 100%;
	left: 0;
	top: 0;
	height: var(--toolbar-height);
	background-color: color('white', 5);
	z-index: 12;
	font-weight: 600;
	display: grid;
	grid-template-columns: 1fr 2fr 1fr;
	align-items: center;

	.page__meta {
		align-items: center;
		justify-content: center;
		display: flex;
		gap: spacing(s5);
	}

	button {
		min-width: var(--toolbar-height);
		height: var(--toolbar-height);
		padding: spacing(s5);
		cursor: pointer;
		transition: 0.2s;
		display: inline-flex;
		align-items: center;
		justify-content: center;

		&:hover {
			background-color: color('white', 0);
		}
	}

	.left__actions {
		justify-content: space-between;
	}

	.right__actions {
		place-self: end;
	}

	.history {
		gap: spacing(s3);

		button {
			svg {
				--icon-size: #{spacing(s6)};
			}
		}

		span {
			overflow: hidden;
			display: -webkit-box;
			-webkit-line-clamp: 1;
			line-clamp: 1;
			-webkit-box-orient: vertical;
		}
	}

	.navigate {
		display: flex;
		align-items: center;
		gap: spacing(s3);
		position: relative;

		p {
			max-width: px-to(301px, rem);
			overflow: hidden;
			display: -webkit-box;
			-webkit-line-clamp: 1;
			line-clamp: 1;
			-webkit-box-orient: vertical;
			word-break: break-all;
		}
	}

	.seperator {
		height: spacing(s4);
		width: px-to(1px, rem);
		background-color: color('grey', 30);
	}

	.breadcrumb {
		gap: spacing(s1);
		a {
			text-transform: capitalize;
		}
	}

	.status {
		padding: spacing(s1);
		color: color('white', 0);
		border-radius: spacing(s1);
		text-transform: capitalize;
		font-weight: 400;

		&[data-status='draft'] {
			background-color: color('grey', 50);
		}

		// This color is temporary, change if needed
		&[data-status='modified'] {
			background-color: color('yellow', 100);
		}

		&[data-status='published'] {
			background-color: color('green');
		}
	}
}

.component {
	&__wrapper {
		padding: spacing(s5) 0;
		height: 100%;
		overflow: auto;
	}
	&__title {
		display: flex;
		align-items: center;
		gap: spacing(s3);
		padding: 0 spacing(s6);
		button {
			cursor: pointer;
			text-align: left;
		}
		h6 {
			display: flex;
			align-items: center;
			gap: spacing(s2);
			width: 58%;
			button {
				display: flex;
				align-items: center;
				gap: spacing(s2);
			}
		}

		.preventries {
			position: relative;
			&__list {
				position: absolute;
				top: 100%;
				left: 0;
				width: px-to(160px, rem);
				background-color: color('white', 0);
				box-shadow: 0 spacing(s1) spacing(s2) rgba(69, 69, 69, 0.04);
				z-index: 1;
				display: flex;
				flex-direction: column;
				border-radius: spacing(s2);
				border: px-to(1px, rem) solid color('grey', 15);
			}
			&__item {
				padding: spacing(s2) spacing(s3);
				width: 100%;
				@include fluid($font-size) {
					font-size: size('button', 'lg');
				}
				&:hover {
					background-color: color('grey', 15);
				}
			}
		}

		button.cur__entry {
			flex: 1;
			overflow: hidden;
			white-space: nowrap;
			display: block;
			text-overflow: ellipsis;
			margin-right: spacing(s2);
		}
	}
	&__action {
		display: flex;
		align-items: center;
		gap: spacing(s2);
		margin-left: auto;
		flex-shrink: 0;
		flex-grow: 0;
		button {
			--size: #{px-to(16px, rem)};
			width: spacing(s5);
			height: spacing(s5);
			display: flex;
			align-items: center;
			justify-content: center;
			color: color('grey', 90);
			cursor: pointer;
		}
	}
}
