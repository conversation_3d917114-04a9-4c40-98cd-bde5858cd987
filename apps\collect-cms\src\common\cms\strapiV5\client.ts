import { cacheWrapper, fetcher } from '@collective/core'
import type { INavigationKind } from '@/contexts/NavigationContext'
import type {
	IMultiDataProps,
	IRelationResultDataProps,
	IResultDataProps,
	ISingleDataProps,
} from '../interface'

export const getCmsDataAdminClient = async <PageProps>({
	path,
	deep = 4,
	locale = 'en',
	draft = false,
	filter,
	ttl,
}: {
	path: string
	deep?: number
	locale?: string
	draft?: boolean
	filter?: string
	ttl?: number
}): Promise<PageProps> => {
	if (typeof window === 'undefined') {
		return Promise.reject(new Error('This function is only available on the client'))
	}
	const url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/${path}?pLevel=${deep}&locale=${locale}${draft ? '&publicationState=preview' : ''}${filter ? `&${filter}` : ''}`
	const options = {
		method: 'GET',
		headers: {
			authorization: `Bearer ${localStorage.getItem('adminJwt')}`,
		},
	}
	return cacheWrapper<PageProps>(url, options, ttl)
}

export const getCmsAdminPageRelationListByQuery = async ({
	uid,
	fieldName,
	documentId,
	pageSize = 50,
	locale = 'en',
	filter,
}: {
	uid: string
	fieldName: string
	documentId: string
	pageSize?: number
	locale?: string
	filter?: string
}): Promise<IMultiDataProps<IRelationResultDataProps>> => {
	if (typeof window === 'undefined') {
		return Promise.reject(new Error('This function is only available on the client'))
	}
	const url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/content-manager/relations/${uid}/${fieldName}?id=${documentId}&pageSize=${pageSize}&locale=${locale}${filter ? `&${filter}` : ''}`
	const options = {
		method: 'GET',
		headers: {
			authorization: `Bearer ${localStorage.getItem('adminJwt')}`,
		},
	}
	return cacheWrapper<IMultiDataProps<IRelationResultDataProps>>(url, options)
}

export const getCmsAdminPageRelationList = async ({
	uid,
	documentId,
	fieldName,
	pageSize = 5,
	locale = 'en',
	filter,
}: {
	uid: string
	documentId: string
	fieldName: string
	pageSize?: number
	locale?: string
	filter?: string
}): Promise<IMultiDataProps<IRelationResultDataProps>> => {
	if (typeof window === 'undefined') {
		return Promise.reject(new Error('This function is only available on the client'))
	}
	const url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/content-manager/relations/${uid}/${documentId}/${fieldName}?pageSize=${pageSize}&locale=${locale}${filter ? `&${filter}` : ''}`
	const options = {
		method: 'GET',
		headers: {
			authorization: `Bearer ${localStorage.getItem('adminJwt')}`,
		},
	}
	return cacheWrapper<IMultiDataProps<IRelationResultDataProps>>(url, options, 15)
}

export const putCmsAdminPageDocument = async ({
	kind: kind0,
	uid,
	documentId,
	data,
}: {
	kind: INavigationKind
	uid: string
	documentId: string
	data: Omit<IResultDataProps, 'status'>
}): Promise<ISingleDataProps<IResultDataProps>> => {
	if (typeof window === 'undefined') {
		return Promise.reject(new Error('This function is only available on the client'))
	}
	const kind = kind0 === 'collectionType' ? 'collection-types' : 'single-types'
	const url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/content-manager/${kind}/api::${uid}.${uid}/${documentId}`
	const options = {
		method: 'PUT',
		headers: {
			'Content-Type': 'application/json',
			authorization: `Bearer ${localStorage.getItem('adminJwt')}`,
		},
		body: JSON.stringify(data),
	}
	return fetcher(url, options)
}

export const publishCmsAdminPageDocument = async ({
	kind: kind0,
	uid,
	documentId,
	data,
}: {
	kind: INavigationKind
	uid: string
	documentId: string
	data: Omit<IResultDataProps, 'status'>
}): Promise<ISingleDataProps<IResultDataProps>> => {
	if (typeof window === 'undefined') {
		return Promise.reject(new Error('This function is only available on the client'))
	}
	const kind = kind0 === 'collectionType' ? 'collection-types' : 'single-types'
	const url = `${process.env.NEXT_PUBLIC_STRAPI_HOST}/content-manager/${kind}/api::${uid}.${uid}/${kind0 === 'collectionType' ? documentId + '/' : ''}actions/publish`
	const options = {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			authorization: `Bearer ${localStorage.getItem('adminJwt')}`,
		},
		body: JSON.stringify(data),
	}
	return fetcher(url, options)
}
