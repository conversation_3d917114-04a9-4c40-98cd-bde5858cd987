@import '@/styles/config';

.wrapper {
	border-radius: spacing(s2);
	background-color: color('grey', 10);
	overflow: hidden;
	position: relative;
	border: 1px solid color('grey', 15);

	.list {
		padding: spacing(s2);
		border-radius: spacing(s2);
		display: flex;
		flex-flow: wrap;

		gap: spacing(s2);
		background-color: color('white', 0);
		border-bottom: 1px solid color('grey', 15);

		.item:hover {
			color: color('white', 0);
			background-color: color('grey', 80);

			.del {
				opacity: 1;
				visibility: visible;
			}
		}
	}

	.item {
		padding: px-to(2px, rem) spacing(s2);
		background-color: color('grey', 10);
		border-radius: spacing(s1);
		display: grid;
		grid-template-columns: 1fr auto;
		align-items: center;
		border-radius: spacing(s8);
		transition: 0.2s;
		position: relative;
		user-select: none;

		p {
			word-break: break-all;
			max-width: px-to(64px, rem);
			overflow: hidden;
			display: -webkit-box;
			-webkit-line-clamp: 1;
			line-clamp: 1;
			-webkit-box-orient: vertical;
		}
	}

	.del {
		cursor: pointer;
		opacity: 0;
		visibility: hidden;
		display: flex;
		border-radius: 50%;
		box-shadow: 0px 2px spacing(s3) 0px #000;
		background-color: inherit;
		position: absolute;
		right: px-to(2px, rem);
		transition-delay: 0.2s;
		background-color: color('grey', 80);
		color: color('white', 0);
		top: 50%;
		transform: translateY(-50%);
		@include fluid($font-size) {
			--size: #{size('button', 'xl')};
		}
		svg {
			padding: spacing(s1);
		}
	}

	.search {
		--input-padding-x: #{spacing(s2)};
		--input-padding-y: #{spacing(s2)};
		--input-height: #{px-to(22px, rem)};

		input {
			box-shadow: none;
			font-weight: 400;
		}

		svg {
			color: color('grey', 40);
			--icon-size: #{px-to(13px, rem)};
		}
	}

	.dropdown {
		padding: 0 spacing(s2) spacing(s2);
		border-radius: spacing(s2);
		gap: spacing(s2);
		display: none;
		flex-flow: wrap;

		.item {
			cursor: pointer;
			background-color: color('grey', 30);
			color: color('white', 0);
		}

		&.active {
			display: flex;
		}
	}
}
