'use client'

import { But<PERSON>, Icon, useIsomorphicLayoutEffect } from '@collective/core'
import type { IComponentProps } from '@collective/integration-lib/cms'
import cn from 'classnames'
import { usePathname, useRouter } from 'next/navigation'
import { useCallback, useContext, useMemo, useState } from 'react'
import { FieldEditor } from '@/components/Builder'
import { PageBuilderContext } from '@/contexts/BuilderContext'
import type { INavigationKind } from '@/contexts/NavigationContext'
import {
	getCmsDataAdminClient,
	publishCmsAdminPageDocument,
	putCmsAdminPageDocument,
	type IResultDataProps,
	type ISingleDataProps,
} from 'common/cms'
import styles from './singletypelayout.module.scss'

export const SingleTypeLayout = ({
	lng,
	kind,
	uid,
	initData,
}: {
	lng: string
	kind: INavigationKind
	uid: string
	initData?: IResultDataProps
}) => {
	const context = useContext(PageBuilderContext)
	const { components, globals, contentType, data, setData } = context
	const { data: cmsData } = data ?? {}
	const fieldSizes = globals.data.fieldSizes
	const contentTypes = contentType.data.schema.attributes
	const [isInit, setIsInit] = useState(false)
	const [isEdit, setIsEdit] = useState(false)
	const [isRefresh, setIsRefresh] = useState(false)
	const router = useRouter()
	const pathname = usePathname()
	const isDraftEnable = useMemo(
		() => contentType.data.schema.draftAndPublish,
		[contentType.data.schema]
	)

	// Filtered component's attributes
	const filteredComponents = (obj: IComponentProps) =>
		Object.entries(obj || {}).filter(([, value]) => typeof value === 'object' && value !== null)

	// Filtered cms data's attributes
	const filteredInfo = (obj: IResultDataProps) =>
		Object.entries(obj || {})
			// Sort key by alphabetically
			.sort((a, b) => a[0].localeCompare(b[0]))
			.filter(([key]) => !['components', 'id'].includes(key))

	const updatedTimeConvert = (datetime: string): string => {
		const seconds = Math.floor((Date.now() - new Date(datetime).getTime()) / 1000)

		const intervals = [
			{ label: 'month', seconds: 2592000 },
			{ label: 'week', seconds: 604800 },
			{ label: 'day', seconds: 86400 },
			{ label: 'hour', seconds: 3600 },
			{ label: 'minute', seconds: 60 },
			{ label: 'second', seconds: 1 },
		]

		const interval = intervals.find((i) => Math.floor(seconds / i.seconds) > 0)

		return interval
			? `${Math.floor(seconds / interval.seconds)} ${interval.label}${Math.floor(seconds / interval.seconds) !== 1 ? 's' : ''} ago`
			: 'just now'
	}

	const handleSave = useCallback(async () => {
		const { status, ...rest } = data.data
		const response = await putCmsAdminPageDocument({
			kind,
			uid: uid,
			documentId: initData?.documentId || '',
			data: { ...rest },
		})
		setIsRefresh(true)
	}, [data.data, initData?.documentId, kind, uid])

	const handlePublish = useCallback(async () => {
		const { status, ...rest } = data.data
		const response = await publishCmsAdminPageDocument({
			kind,
			uid: uid,
			documentId: initData?.documentId || '',
			data: { ...rest },
		})
		setIsRefresh(true)
	}, [data.data, initData?.documentId, kind, uid])

	useIsomorphicLayoutEffect(() => {
		if (initData && !isInit) {
			setData({ data: initData })
			setIsInit(true)
			return
		}
		const fetchData = async () => {
			// Need implement cache for these data
			const res = await getCmsDataAdminClient<ISingleDataProps<IResultDataProps>>({
				path: `content-manager/single-types/api::${uid}.${uid}`,
				deep: 4,
				locale: lng,
				ttl: isRefresh ? 0 : 60,
			})
			setData(res)
			if (isRefresh) setIsRefresh(false)
		}
		fetchData()
	}, [uid, lng, initData, isInit, isRefresh])
	// console.log(lng, uid)

	// useIsomorphicLayoutEffect(() => {
	// 	console.log('Get updated data:', data)
	// }, [data])

	if (!cmsData) {
		return (
			<div className={styles.wrapper}>
				<div className={styles.layout__editor}>
					<div className={styles.editor}>
						<div className={styles.editor__section}>
							<h6 className="collect__heading collect__heading--h6">Loading...</h6>
						</div>
					</div>
				</div>
			</div>
		)
	}
	// console.log(context)
	return (
		<div className={styles.wrapper}>
			<div className={styles.layout__editor}>
				<div className={styles.editor}>
					{cmsData.components.map((component, idx) => {
						const cmpData = components.data.find((comp) => comp.uid === component.__component)
						if (!cmpData) return null // Không có component tương ứng
						return (
							<div key={idx} className={styles.editor__section}>
								<h6 className="collect__heading collect__heading--h6">
									{cmpData.schema.displayName || cmpData.uid}
								</h6>
								<div className={styles.editor__components}>
									{filteredComponents(cmpData.schema.attributes).map(([key, value]) => {
										const val = value as {
											type: string
										}
										// console.log(key, value, component[key])
										return (
											<FieldEditor
												key={key}
												{...val}
												name={`${key} ${Array.isArray(component[key]) ? `(${component[key].length})` : ''}`}
												size={fieldSizes[val.type as keyof typeof fieldSizes]?.default}
												value={component[key]}
												onChange={(props) => {
													setData((prevData) => {
														const newData = { ...prevData }
														const { field, value } = props
														const curCmpIndex = newData.data.components.findIndex(
															(data) =>
																data.__component === component.__component &&
																data.id === component.id
														)
														if (curCmpIndex === -1) {
															return newData
														}

														newData.data.components[curCmpIndex] = {
															...newData.data.components[curCmpIndex],
															[field.trim()]: value,
														}
														return newData
													})
												}}
											/>
										)
									})}
								</div>
							</div>
						)
					})}
				</div>
			</div>

			<div className={styles.layout__info}>
				<div className="collect__buttons">
					<Button
						className={cn('collect__button', 'collect__button--lg', 'black')}
						onClick={() => {
							const newPath = pathname.replace('content-manager', 'content-builder').split('/')
							if (newPath[newPath.length - 1] === 'edit') {
								newPath.pop()
							}
							router.push(newPath.join('/'))
						}}
					>
						Switch to Builder Mode
					</Button>
					{isDraftEnable && (
						<Button className={cn('collect__button', 'collect__button--lg')} onClick={handleSave}>
							Save as Draft
						</Button>
					)}
					{isDraftEnable && (
						<Button
							className={cn('collect__button', 'collect__button--lg', 'yellow')}
							onClick={handlePublish}
						>
							Publish
						</Button>
					)}
					{!isDraftEnable && (
						<Button
							className={cn('collect__button', 'collect__button--lg', 'yellow')}
							onClick={handleSave}
						>
							Save
						</Button>
					)}
					<small className={styles.lastupdate}>
						Updated {updatedTimeConvert(cmsData.updatedAt as string)}
					</small>
				</div>
				<div className={styles.info}>
					<div className={styles.info__header}>
						<p>Page Info</p>
						<Button
							className={cn('collect__button', 'collect__button--md', 'yellow')}
							onClick={() => setIsEdit(true)}
						>
							<Icon type="cms" variant="edit" />
							Edit
						</Button>
					</div>
					<div className={styles.info__body}>
						{filteredInfo(cmsData).map(([key, value]) => (
							<div key={key} className={styles.info__item}>
								<div className={styles.info__label}>
									<p>{key}</p>
									<Icon type="cms" variant="info" />
								</div>
								<div className={cn(styles.info__value)}>
									{
										typeof value === 'string' || typeof value === 'number'
											? value
											: 'unformated data' // : JSON.stringify(value)
									}
								</div>
							</div>
						))}
					</div>
				</div>
			</div>

			{isEdit && (
				<div className={styles.layout__popup}>
					<div className={styles.popup}>
						<div className={styles.popup__header}>
							<button onClick={() => setIsEdit(false)}>
								<Icon type="cms" variant="chevron-left" /> Back
							</button>
							<div className={styles.popup__title}>
								<h2 className="collect__heading">Edit Page Info</h2>
								<Button onClick={() => setIsEdit(false)} className="collect__button yellow">
									Save Settings
								</Button>
							</div>
						</div>
						<div className={styles.popup__body}>
							{filteredInfo(cmsData).map(([key, value]) => {
								if (!contentTypes[key]?.type) return null
								return (
									<FieldEditor
										key={key}
										{...contentTypes[key]}
										name={key}
										size={fieldSizes[contentTypes[key]?.type as keyof typeof fieldSizes]?.default}
										value={value}
										onChange={(props) => {
											setData((prevData) => {
												const newData = { ...prevData }
												const { field, value } = props

												newData.data = {
													...newData.data,
													[field.trim()]: value,
												}
												return newData
											})
										}}
									/>
								)
							})}
						</div>
					</div>
				</div>
			)}
		</div>
	)
}
