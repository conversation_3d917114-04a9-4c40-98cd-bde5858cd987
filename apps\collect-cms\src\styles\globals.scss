@import './config';

:root {
	--collect-primary-color-80: #{color('yellow', 80)};
	--ease-transition: cubic-bezier(0.34, 1.56, 0.64, 1);
	--ease-transition-2: cubic-bezier(0.22, 1, 0.36, 1);
	--toolbar-height: #{px-to(60px, rem)};
}

html,
body {
	color: color('grey', 90);
	@include fluid($font-size) {
		font-size: size('body', 'md');
	}
	line-height: 1.4;
}

.collect__grid {
	display: grid;
	gap: spacing('s4');
	margin-left: auto;
	margin-right: auto;
	width: 100%;

	@include min-width('md') {
		column-gap: spacing('s5');
	}

	&.grid__layout {
		@include min-width('sm') {
			grid-template-columns: repeat(6, 1fr);
		}

		@include min-width('md') {
			grid-template-columns: repeat(12, 1fr);
		}
	}
}

[class^='collect__heading'] {
	font-weight: 600;
	line-height: normal;
}

h1.collect__heading,
.collect__heading--h1 {
	@include fluid($font-size) {
		font-size: size('heading', 'h1');
	}
}

h2.collect__heading,
.collect__heading--h2 {
	@include fluid($font-size) {
		font-size: size('heading', 'h2');
	}
}

h3.collect__heading,
.collect__heading--h3 {
	@include fluid($font-size) {
		font-size: size('heading', 'h3');
	}
}

h4.collect__heading,
.collect__heading--h4 {
	@include fluid($font-size) {
		font-size: size('heading', 'h4');
	}
}

h5.collect__heading,
.collect__heading--h5 {
	@include fluid($font-size) {
		font-size: size('heading', 'h5');
	}
}

h6.collect__heading,
.collect__heading--h6 {
	@include fluid($font-size) {
		font-size: size('heading', 'h6');
	}
}

[class^='collect__body'] {
	line-height: 1.4;

	&.semi__bold {
		font-weight: 600;
	}
}

.collect__body--md {
	@include fluid($font-size) {
		font-size: size('body', 'md');
	}
}

.collect__body--lg {
	@include fluid($font-size) {
		font-size: size('body', 'lg');
	}
}
.collect__body--sm {
	@include fluid($font-size) {
		font-size: size('body', 'sm');
	}
}
.collect__body--xs {
	@include fluid($font-size) {
		font-size: size('body', 'xs');
	}
}

[class^='collect__label'] {
	font-weight: 600;
}

.collect__label {
	@include fluid($font-size) {
		font-size: size('label', 'subheader');
	}
	line-height: 1.3;
}

.collect__label--uppercase {
	@include fluid($font-size) {
		font-size: size('label', 'up-subheader');
	}
	text-transform: uppercase;
}

button.collect__button {
	padding: spacing(s2) spacing(s6);
	background-color: color('white', 0);
	color: color('grey', 70);
	border-radius: spacing(s2);
	box-shadow: 0 spacing(s1) spacing(s6) rgba(69, 69, 69, 0.15);
	span {
		display: flex;
		align-items: center;
		gap: spacing(s1);
	}
	&--sm {
		@include fluid($font-size) {
			font-size: size('button', 'sm');
		}
		// text-decoration: underline;
		font-weight: 600;
	}
	&--md {
		@include fluid($font-size) {
			font-size: size('button', 'md');
		}
		font-weight: 600;
	}
	&--lg {
		@include fluid($font-size) {
			font-size: size('button', 'lg');
		}
		font-weight: 600;
	}

	&.yellow {
		background-color: color('yellow', 80);
		color: color('grey', 90);
	}
	&.black {
		background-color: color('black', 0);
		color: color('white', 0);
	}
}

.collect__buttons {
	display: grid;
	gap: spacing(s3);
}

// .collect__button--md {
// 	@include fluid($font-size) {
// 		font-size: size('button', 'md');
// 	}
// 	font-weight: 600;
// }

// .collect__button--lg {
// 	@include fluid($font-size) {
// 		font-size: size('button', 'lg');
// 	}
// 	font-weight: 600;
// }

// .collect__button--sm {
// 	@include fluid($font-size) {
// 		font-size: size('button', 'md');
// 	}
// 	text-decoration: underline;
// }

.collect__subheader {
	@include fluid($font-size) {
		font-size: size('label', 'subheader');
	}
	font-weight: 600;
	line-height: 1.3;
	text-transform: capitalize;
	span {
		font-weight: 600;
		margin-left: px-to(2px, rem);
	}
}

.text-w-icon {
	--icon-gap: #{spacing(s2)};
	display: flex;
	align-items: center;
	gap: var(--icon-gap);
}

.content__manager {
	display: grid;
	gap: px-to(40px, rem);
}

.page__header {
	display: grid;
	gap: spacing(s6);
	.text-w-icon {
		cursor: pointer;
		--icon-gap: 0;
	}
}

.page__headline {
	h1 {
		display: inline;
	}

	a {
		vertical-align: 25%;
		margin-left: px-to(10px, rem);
	}

	svg {
		--icon-size: #{spacing(s6)};
	}
}

.flex__center {
	align-items: center;
	justify-content: center;
	display: flex;
}

.text__w--icon {
	--icon-gap: #{spacing(s3)};
	align-items: center;
	justify-content: center;
	display: flex;
	gap: var(--icon-gap);

	&.align__center {
		align-items: center;
	}
}

div.collect__input {
	--input-height: #{px-to(41px, rem)};
	--input-border-color: transparent;
	--input-radius: #{spacing(s2)};
	--input-bg: #{color('white', 0)};
	--input-padding-x: #{spacing(s4)};
	--input-padding-y: #{spacing(s2)};
	width: 100%;

	// flex: 0 0 px-to(360px, rem);

	&.clickable {
		cursor: pointer;
	}

	&-group {
		display: flex;
		align-items: baseline;
		gap: spacing(s2);
		&.stacked {
			flex-direction: column;
		}
	}

	&.has__border {
		--input-border-color: #{color('grey', 15)};
	}

	&::placeholder {
		color: color('grey', 40);
	}
	span {
		pointer-events: auto !important;
	}
	span svg,
	svg {
		cursor: pointer;
		--size: #{spacing(s4)};
		color: color('grey', 50);
	}

	input {
		font-weight: 400;
		&::placeholder {
			color: color('grey', 30);
			font-weight: 400;
		}
		&::-webkit-datetime-edit {
			display: block;
			width: 100%;
		}
		&::-webkit-inner-spin-button,
		&::-webkit-calendar-picker-indicator {
			display: none;
			-webkit-appearance: none;
		}
	}
}

div.select__dropdown {
	--dropdown-bg-color: #{color('white', 0)};
	z-index: 11;
}

.ck.ck-editor__editable_inline {
	min-height: px-to(120px, rem);
}