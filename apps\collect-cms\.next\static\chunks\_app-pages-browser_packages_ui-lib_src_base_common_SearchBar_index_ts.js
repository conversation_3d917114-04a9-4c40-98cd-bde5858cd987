/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_packages_ui-lib_src_base_common_SearchBar_index_ts"],{

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/SearchBar/searchbar.module.scss":
/*!*****************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/SearchBar/searchbar.module.scss ***!
  \*****************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"wrapper\":\"searchbar_wrapper__lQzqp\",\"search__wrapper\":\"searchbar_search__wrapper__zhJNv\",\"search__bar\":\"searchbar_search__bar__me_P5\"};\n    if(true) {\n      // 1748453773064\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  \nmodule.exports.__checksum = \"0a17342813ae\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL1NlYXJjaEJhci9zZWFyY2hiYXIubW9kdWxlLnNjc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxrQkFBa0I7QUFDbEIsT0FBTyxJQUFVO0FBQ2pCO0FBQ0Esc0JBQXNCLG1CQUFPLENBQUMsd01BQW1KLGNBQWMsc0RBQXNEO0FBQ3JQLE1BQU0sVUFBVTtBQUNoQjtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vU2VhcmNoQmFyL3NlYXJjaGJhci5tb2R1bGUuc2Nzcz8wYTI2Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIGV4dHJhY3RlZCBieSBtaW5pLWNzcy1leHRyYWN0LXBsdWdpblxubW9kdWxlLmV4cG9ydHMgPSB7XCJ3cmFwcGVyXCI6XCJzZWFyY2hiYXJfd3JhcHBlcl9fbFF6cXBcIixcInNlYXJjaF9fd3JhcHBlclwiOlwic2VhcmNoYmFyX3NlYXJjaF9fd3JhcHBlcl9femhKTnZcIixcInNlYXJjaF9fYmFyXCI6XCJzZWFyY2hiYXJfc2VhcmNoX19iYXJfX21lX1A1XCJ9O1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIC8vIDE3NDg0NTM3NzMwNjRcbiAgICAgIHZhciBjc3NSZWxvYWQgPSByZXF1aXJlKFwiRDovQ0RBL3JlcG9zL2JyYW5kLWNvbXBhc3MtZnJvbnRlbmQtdGVtcGxhdGUvYXBwcy9jb2xsZWN0LWNtcy9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL21pbmktY3NzLWV4dHJhY3QtcGx1Z2luL2htci9ob3RNb2R1bGVSZXBsYWNlbWVudC5qc1wiKShtb2R1bGUuaWQsIHtcInB1YmxpY1BhdGhcIjpcIi9fbmV4dC9cIixcImVzTW9kdWxlXCI6ZmFsc2UsXCJsb2NhbHNcIjp0cnVlfSk7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoY3NzUmVsb2FkKTtcbiAgICAgIFxuICAgIH1cbiAgXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCIwYTE3MzQyODEzYWVcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/SearchBar/searchbar.module.scss\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/SearchBar/SearchBar.tsx":
/*!*********************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/SearchBar/SearchBar.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SearchBar: function() { return /* binding */ SearchBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Input!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Input/Input.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Input!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./searchbar.module.scss */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/SearchBar/searchbar.module.scss\");\n/* harmony import */ var _searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2__);\nvar _this = undefined;\n\n\n\n\nvar SearchBar = function(param) {\n    var cid = param.cid, Headline = param.Headline;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().wrapper)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"aidigi__grid\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().search__wrapper),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().headline), \"aidigi__heading--h3\"),\n                        children: Headline\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\SearchBar\\\\SearchBar.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().search__bar)),\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_4__.Icon, {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_searchbar_module_scss__WEBPACK_IMPORTED_MODULE_2___default().search__icon)),\n                            variant: \"search-icon\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\SearchBar\\\\SearchBar.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 17\n                        }, void 0),\n                        placeholder: \"Logo usages\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\SearchBar\\\\SearchBar.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\SearchBar\\\\SearchBar.tsx\",\n                lineNumber: 13,\n                columnNumber: 4\n            }, _this)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\SearchBar\\\\SearchBar.tsx\",\n            lineNumber: 12,\n            columnNumber: 3\n        }, _this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\SearchBar\\\\SearchBar.tsx\",\n        lineNumber: 11,\n        columnNumber: 2\n    }, _this);\n};\n_c = SearchBar;\nvar _c;\n$RefreshReg$(_c, \"SearchBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/SearchBar/SearchBar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/SearchBar/index.ts":
/*!****************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/SearchBar/index.ts ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SearchBar: function() { return /* reexport safe */ _SearchBar__WEBPACK_IMPORTED_MODULE_0__.SearchBar; }\n/* harmony export */ });\n/* harmony import */ var _SearchBar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SearchBar */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/SearchBar/SearchBar.tsx\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL1NlYXJjaEJhci9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUEyQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9TZWFyY2hCYXIvaW5kZXgudHM/YmU2ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL1NlYXJjaEJhcidcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/SearchBar/index.ts\n"));

/***/ })

}]);