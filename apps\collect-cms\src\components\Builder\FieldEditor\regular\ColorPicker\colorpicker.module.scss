@import '@/styles/config';

.wrapper {
	position: relative;
	&.builder {
		display: grid;
		gap: spacing(s4);
		.color__picker {
			position: static;
			width: 100%;
			:global(.react-colorful) {
				width: 100%;
			}
		}
	}
}

.color {
	&__dot {
		border-radius: spacing(s2);
		border: px-to(1px, rem) solid color('grey', 30);
		width: spacing(s4);
		height: spacing(s4);
		background-color: var(--color);
	}
	&__picker {
		display: none;
		position: absolute;
		left: calc(100% + spacing(s2));
		top: 0;
		gap: spacing(s2);
		z-index: 10;
		&.active {
			display: grid;
		}
		:global(button.collect__button) {
			margin: 0 !important;
			cursor: pointer;
		}
	}
}
