'use client'

import path from 'path'
import { useIsomorphicLayoutEffect } from '@collective/core'
import cn from 'classnames'
import { useRouter, usePathname } from 'next/navigation'
import { useState } from 'react'
import { PageBuilderProvider, type BuilderBaseProps } from '@/contexts/BuilderContext'
import type { INavigationKind, INavigationLayoutProps } from '@/contexts/NavigationContext'
import { CollectionTypeLayout } from './CollectionTypeLayout'
import styles from './contentbuilderlayout.module.scss'
import { SingleTypeLayout } from './SingleTypeLayout'

export const ContentBuilderlayout = ({
	lng,
	value,
	data,
	activeTabIndex = 0,
}: {
	lng: string
	value: BuilderBaseProps
	data: INavigationLayoutProps[]
	activeTabIndex: number
}) => {
	const { slug } = value
	const router = useRouter()
	const pathname = usePathname()
	const [currentTab, setCurrentTab] = useState(data[activeTabIndex === -1 ? 0 : activeTabIndex])

	const handleTabChange = (tab: (typeof data)[0]) => {
		if (slug.length >= 4 && slug[slug.length - 1] === 'edit' && currentTab) {
			router.push(`${pathname.split('/').slice(0, 3).join('/')}/${currentTab.apiId}`)
		}
		setCurrentTab(tab)
	}

	// Set link to correct apiId of current tab
	useIsomorphicLayoutEffect(() => {
		if (!currentTab) {
			return
		}
		let newPath = pathname
		if (slug.length >= 3 && slug[1] === currentTab.apiId && slug[slug.length - 1] === 'edit') {
			return
		}
		if (currentTab.defaultMode === 'builder' && currentTab.kind === 'singleType') {
			newPath = newPath.replace('content-manager', 'content-builder')
			router.push(`${newPath.split('/').slice(0, 3).join('/')}/${currentTab.apiId}`)
		} else {
			router.push(`${newPath.split('/').slice(0, 3).join('/')}/${currentTab.apiId}`)
		}
	}, [pathname, currentTab])

	return (
		<PageBuilderProvider value={value}>
			<div className={cn(styles.wrapper)}>
				<div className={styles.navigation}>
					{data.map((item, index) => {
						return (
							<button
								key={index}
								className={cn(currentTab?.uid === item.uid && styles.active, 'collect__button--lg')}
								onClick={() => handleTabChange(item)}
							>
								{item.kind === 'singleType' ? `Edit ${item.name}` : `Database ${item.name}`}
							</button>
						)
					})}
				</div>
				<div className={styles.content}>
					{currentTab && currentTab.kind === 'collectionType' ? (
						<CollectionTypeLayout lng={lng} uid={currentTab.uid} />
					) : currentTab && currentTab.kind === 'singleType' ? (
						<SingleTypeLayout lng={lng} kind="singleType" uid={currentTab.uid} />
					) : null}
				</div>
			</div>
		</PageBuilderProvider>
	)
}
