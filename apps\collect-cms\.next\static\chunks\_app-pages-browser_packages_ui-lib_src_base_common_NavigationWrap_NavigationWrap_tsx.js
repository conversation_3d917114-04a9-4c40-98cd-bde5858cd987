/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_packages_ui-lib_src_base_common_NavigationWrap_NavigationWrap_tsx"],{

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/NavigationWrap/navigationwrap.module.scss":
/*!***************************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/NavigationWrap/navigationwrap.module.scss ***!
  \***************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"wrapper\":\"navigationwrap_wrapper__CWlTa\",\"nav__wrapper\":\"navigationwrap_nav__wrapper__23AWi\",\"row__nav\":\"navigationwrap_row__nav__N15Ox\",\"col__nav\":\"navigationwrap_col__nav__9d88W\",\"nav__list\":\"navigationwrap_nav__list__L5XYj\"};\n    if(true) {\n      // 1748453773070\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  \nmodule.exports.__checksum = \"edf51fc95030\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL05hdmlnYXRpb25XcmFwL25hdmlnYXRpb253cmFwLm1vZHVsZS5zY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0Esa0JBQWtCO0FBQ2xCLE9BQU8sSUFBVTtBQUNqQjtBQUNBLHNCQUFzQixtQkFBTyxDQUFDLHdNQUFtSixjQUFjLHNEQUFzRDtBQUNyUCxNQUFNLFVBQVU7QUFDaEI7QUFDQTtBQUNBO0FBQ0EseUJBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL05hdmlnYXRpb25XcmFwL25hdmlnYXRpb253cmFwLm1vZHVsZS5zY3NzPzgyYmEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZXh0cmFjdGVkIGJ5IG1pbmktY3NzLWV4dHJhY3QtcGx1Z2luXG5tb2R1bGUuZXhwb3J0cyA9IHtcIndyYXBwZXJcIjpcIm5hdmlnYXRpb253cmFwX3dyYXBwZXJfX0NXbFRhXCIsXCJuYXZfX3dyYXBwZXJcIjpcIm5hdmlnYXRpb253cmFwX25hdl9fd3JhcHBlcl9fMjNBV2lcIixcInJvd19fbmF2XCI6XCJuYXZpZ2F0aW9ud3JhcF9yb3dfX25hdl9fTjE1T3hcIixcImNvbF9fbmF2XCI6XCJuYXZpZ2F0aW9ud3JhcF9jb2xfX25hdl9fOWQ4OFdcIixcIm5hdl9fbGlzdFwiOlwibmF2aWdhdGlvbndyYXBfbmF2X19saXN0X19MNVhZalwifTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICAvLyAxNzQ4NDUzNzczMDcwXG4gICAgICB2YXIgY3NzUmVsb2FkID0gcmVxdWlyZShcIkQ6L0NEQS9yZXBvcy9icmFuZC1jb21wYXNzLWZyb250ZW5kLXRlbXBsYXRlL2FwcHMvY29sbGVjdC1jbXMvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9taW5pLWNzcy1leHRyYWN0LXBsdWdpbi9obXIvaG90TW9kdWxlUmVwbGFjZW1lbnQuanNcIikobW9kdWxlLmlkLCB7XCJwdWJsaWNQYXRoXCI6XCIvX25leHQvXCIsXCJlc01vZHVsZVwiOmZhbHNlLFwibG9jYWxzXCI6dHJ1ZX0pO1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGNzc1JlbG9hZCk7XG4gICAgICBcbiAgICB9XG4gIFxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiZWRmNTFmYzk1MDMwXCJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/NavigationWrap/navigationwrap.module.scss\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/NavigationWrap/NavigationWrap.tsx":
/*!*******************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/NavigationWrap/NavigationWrap.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavigationWrap: function() { return /* binding */ NavigationWrap; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./navigationwrap.module.scss */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/NavigationWrap/navigationwrap.module.scss\");\n/* harmony import */ var _navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n// import { useIsomorphicLayoutEffect } from '@collective/core'\n// import type { INavigationWrapProps } from '@collective/integration-lib/cms'\n// import { searchNavigation } from '@collective/integration-lib/search/meilisearch'\nvar _this = undefined;\n\n\n// import type { Hits } from 'meilisearch'\n\n// import { useState } from 'react'\n\nvar NavigationWrap = function(param) {\n    var cid = param.cid, List0 = param.List;\n    // Strapi V5's bug that shows both draft and published data\n    var List = List0.filter(function(item) {\n        return item.publishedAt !== null;\n    });\n    // const [navigation, setNavigation] = useState<Hits<INavigationProps>>([\n    // \t{\n    // \t\tid: -99,\n    // \t\tHeadline: '',\n    // \t\tslug: '',\n    // \t\tPages: [\n    // \t\t\t{\n    // \t\t\t\tHeadline: '',\n    // \t\t\t\tslug: '',\n    // \t\t\t},\n    // \t\t],\n    // \t},\n    // ])\n    // const searchNavigationWrap = async () => {\n    // \tconst data = await searchNavigation('', {\n    // \t\thitsPerPage: List.data.attributes.map((item) => item).length,\n    // \t\tpage: 1,\n    // \t})\n    // \tsetNavigation(data.hits)\n    // }\n    // useIsomorphicLayoutEffect(() => {\n    // \tsearchNavigationWrap()\n    // }, [])\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: cid,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3___default().wrapper)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"aidigi__grid\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3___default().nav__wrapper),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3___default().row__nav),\n                    children: List === null || List === void 0 ? void 0 : List.map(function(item, idx) {\n                        var Headline = item.Headline, slug = item.slug, Pages = item.Pages;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3___default().col__nav),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"aidigi__heading\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/\".concat(slug),\n                                        children: Headline\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 11\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 10\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: (_navigationwrap_module_scss__WEBPACK_IMPORTED_MODULE_3___default().nav__list),\n                                    style: {\n                                        gridTemplateColumns: // List divide 2 columns for (n - 1) % 3 === 0 and last item\n                                        idx % 3 === 0 && idx === List.map(function(item) {\n                                            return item;\n                                        }).length - 1 ? \"repeat(2, 1fr)\" : \"\"\n                                    },\n                                    children: Pages === null || Pages === void 0 ? void 0 : Pages.map(function(child, count) {\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/\".concat(child.slug, \"/\").concat(child.slug),\n                                                children: child.Headline\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 13\n                                            }, _this)\n                                        }, count, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 12\n                                        }, _this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 10\n                                }, _this)\n                            ]\n                        }, idx, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 9\n                        }, _this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 6\n                }, _this)\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n                lineNumber: 54,\n                columnNumber: 5\n            }, _this)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n            lineNumber: 53,\n            columnNumber: 4\n        }, _this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\NavigationWrap\\\\NavigationWrap.tsx\",\n        lineNumber: 52,\n        columnNumber: 3\n    }, _this);\n};\n_c = NavigationWrap;\nvar _c;\n$RefreshReg$(_c, \"NavigationWrap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/NavigationWrap/NavigationWrap.tsx\n"));

/***/ })

}]);