"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(dashboard)/content-manager/[...slug]/page",{

/***/ "(app-pages-browser)/./src/components/Builder/FieldEditor/regular/RichText/RichText.tsx":
/*!**************************************************************************!*\
  !*** ./src/components/Builder/FieldEditor/regular/RichText/RichText.tsx ***!
  \**************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RichText: function() { return /* binding */ RichText; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _ckeditor_ckeditor5_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ckeditor/ckeditor5-react */ \"(app-pages-browser)/../../node_modules/@ckeditor/ckeditor5-react/dist/index.js\");\n/* harmony import */ var _barrel_optimize_names_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var ckeditor5__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ckeditor5 */ \"(app-pages-browser)/../../node_modules/ckeditor5/dist/ckeditor5.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var ckeditor5_ckeditor5_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ckeditor5/ckeditor5.css */ \"(app-pages-browser)/../../node_modules/ckeditor5/dist/ckeditor5.css\");\n/* harmony import */ var _richtext_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./richtext.module.scss */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/RichText/richtext.module.scss\");\n/* harmony import */ var _richtext_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_richtext_module_scss__WEBPACK_IMPORTED_MODULE_5__);\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nvar RichText = function(props) {\n    _s();\n    var type = props.type, value = props.value, onChange = props.onChange, name = props.name;\n    var propsType = type !== null && type !== void 0 ? type : \"\";\n    // Xử lý giá trị ban đầu một cách rõ ràng hơn\n    var initialValue = \"\";\n    if (value !== undefined && value !== null) {\n        initialValue = propsType === \"json\" ? JSON.stringify(value) : String(value);\n    }\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(initialValue), 2), propsValue = _useState[0], setPropsValue = _useState[1];\n    // Tạo config mà không có initialData\n    var config = {\n        licenseKey: \"GPL\",\n        plugins: [\n            ckeditor5__WEBPACK_IMPORTED_MODULE_2__.Essentials,\n            ckeditor5__WEBPACK_IMPORTED_MODULE_2__.Paragraph,\n            ckeditor5__WEBPACK_IMPORTED_MODULE_2__.Bold,\n            ckeditor5__WEBPACK_IMPORTED_MODULE_2__.Italic,\n            ckeditor5__WEBPACK_IMPORTED_MODULE_2__.Underline,\n            ckeditor5__WEBPACK_IMPORTED_MODULE_2__.Strikethrough,\n            ckeditor5__WEBPACK_IMPORTED_MODULE_2__.Link,\n            ckeditor5__WEBPACK_IMPORTED_MODULE_2__.Code,\n            ckeditor5__WEBPACK_IMPORTED_MODULE_2__.SourceEditing,\n            ckeditor5__WEBPACK_IMPORTED_MODULE_2__.Typing,\n            ckeditor5__WEBPACK_IMPORTED_MODULE_2__.Enter,\n            ckeditor5__WEBPACK_IMPORTED_MODULE_2__.Clipboard,\n            ckeditor5__WEBPACK_IMPORTED_MODULE_2__.Undo\n        ],\n        toolbar: [\n            \"undo\",\n            \"redo\",\n            \"|\",\n            \"bold\",\n            \"italic\",\n            \"underline\",\n            \"strikethrough\",\n            \"|\",\n            \"link\",\n            \"code\",\n            \"sourceEditing\"\n        ]\n    };\n    // Đảm bảo rằng data không bao giờ là undefined hoặc null\n    var editorData = propsValue || \"\";\n    (0,_barrel_optimize_names_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect)(function() {\n        props.value !== propsValue && setPropsValue(props.value);\n    }, [\n        props.value\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_richtext_module_scss__WEBPACK_IMPORTED_MODULE_5___default().wrapper),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ckeditor_ckeditor5_react__WEBPACK_IMPORTED_MODULE_1__.CKEditor, {\n            editor: ckeditor5__WEBPACK_IMPORTED_MODULE_2__.ClassicEditor,\n            data: editorData,\n            config: config,\n            onChange: function(_event, editor) {\n                var newData = editor.getData();\n                setPropsValue(newData);\n                onChange === null || onChange === void 0 ? void 0 : onChange({\n                    field: name,\n                    value: newData\n                });\n            }\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\RichText\\\\RichText.tsx\",\n            lineNumber: 83,\n            columnNumber: 4\n        }, _this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\RichText\\\\RichText.tsx\",\n        lineNumber: 82,\n        columnNumber: 3\n    }, _this);\n};\n_s(RichText, \"l4ef4LbW/tWeGYDTnandBE1n3Lo=\", false, function() {\n    return [\n        _barrel_optimize_names_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect\n    ];\n});\n_c = RichText;\nvar _c;\n$RefreshReg$(_c, \"RichText\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/FieldEditor/regular/RichText/RichText.tsx\n"));

/***/ })

});