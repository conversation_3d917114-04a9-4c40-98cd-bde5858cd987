import { Button, Icon, Image, Input, useIsomorphicLayoutEffect } from '@collective/core'
import type { IMediaProps } from '@collective/integration-lib/cms'
import cn from 'classnames'
import { usePathname } from 'next/navigation'
import { useContext, useMemo, useState } from 'react'
import { PageBuilderContext } from '@/contexts/BuilderContext'
import { checkArr, formatDate, formatExt, type MediaToolType, useMediaHandlers } from './Media'
import styles from './media.module.scss'

type MediaInfoLayerProps = {
	multiple?: boolean
	toolbar: MediaToolType[]
	mediaList: IMediaProps<'multiple' | 'single'>
	propsValue: IMediaProps<'multiple' | 'single'>
	setPropsValue: React.Dispatch<React.SetStateAction<IMediaProps<'multiple' | 'single'>>>
	currentMediaIdx: number
	setCurrentMediaIdx: React.Dispatch<React.SetStateAction<number>>
	onChange?: (props: { field: string; value: unknown }) => void
	field?: string
}

export const MediaInfoLayer = ({
	multiple,
	toolbar,
	mediaList,
	propsValue,
	setPropsValue,
	currentMediaIdx,
	setCurrentMediaIdx,
	onChange,
	field,
}: MediaInfoLayerProps) => {
	const pathname = usePathname()
	const context = useContext(PageBuilderContext)
	const { mediaInfoData, setMediaInfoData, setActiveMediaId, layerPos } = context
	const { size, width, height, publishedAt, ext, name, alternativeText, caption } = mediaInfoData
	const isBuilderMode = useMemo(() => pathname?.startsWith('/content-builder/'), [pathname])
	// Use shared media handlers
	const { handleAction, handleNextMedia, handlePrevMedia } = useMediaHandlers(
		propsValue,
		setPropsValue,
		currentMediaIdx,
		setCurrentMediaIdx,
		multiple,
		onChange,
		field
	)

	const [fixedInfo, setFixedInfo] = useState({
		size: '',
		dimensions: '',
		date: '',
		extension: '',
	})
	const [editableInfo, setEditableInfo] = useState({
		fileName: '',
		altText: '',
		caption: '',
	})

	const handleSave = () => {
		if (!mediaInfoData || !onChange) return

		// Update the current media with edited information
		const updatedMedia = {
			...mediaInfoData,
			name: editableInfo.fileName
				? `${editableInfo.fileName}${mediaInfoData.ext || ''}`
				: mediaInfoData.name,
			alternativeText: editableInfo.altText || mediaInfoData.alternativeText,
			caption: editableInfo.caption || mediaInfoData.caption,
		}

		// Update propsValue with the modified media
		if (multiple && checkArr(propsValue)) {
			const newArray = [...propsValue]
			newArray[currentMediaIdx] = updatedMedia
			setPropsValue(newArray as IMediaProps<'multiple'>)
			onChange({ field: field || 'media', value: newArray })
		} else {
			setPropsValue(updatedMedia as IMediaProps<'single'>)
			onChange({ field: field || 'media', value: updatedMedia })
		}

		// Update context with the new media info
		setMediaInfoData(updatedMedia)

		// Close MediaInfoLayer after saving
		setActiveMediaId(null)
		setMediaInfoData({ name: '', url: '' })
	}

	const handleOnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value } = e.target
		setEditableInfo((prev) => ({ ...prev, [name]: value }))
	}

	useIsomorphicLayoutEffect(() => {
		setFixedInfo({
			size: `${size}KB`,
			dimensions: `${width}X${height}`,
			date: formatDate(publishedAt as string),
			extension: formatExt(ext || ''),
		})
		setEditableInfo({
			fileName: name?.split('.').slice(0, -1).join('.'),
			altText: alternativeText || '',
			caption: caption || '',
		})
	}, [mediaInfoData])

	return (
		<div
			className={cn(
				styles.info,
				isBuilderMode ? styles.info__builder : '',
				layerPos !== '' ? styles[layerPos] : ''
			)}
			style={
				{
					'--info-cols': isBuilderMode ? 12 : 4,
				} as React.CSSProperties
			}
		>
			{isBuilderMode && (
				<>
					<div className={styles.info__title}>
						<button
							onClick={() => {
								setActiveMediaId(null)
								setMediaInfoData({ name: '', url: '' })
							}}
						>
							<Icon type="cms" variant="back" />
						</button>
						<h6 className="collect__heading collect__heading--h6">Media info</h6>
					</div>
					<div className={styles.info__media}>
						<div className={cn(styles.body, multiple ? styles.detailed__multi : styles.detailed)}>
							{mediaInfoData ? (
								<div
									className={styles.item}
									style={
										{
											'--height': isBuilderMode ? '120px' : '324px',
										} as React.CSSProperties
									}
								>
									<span className={styles.tag}>{formatExt(mediaInfoData.ext || '')}</span>
									<div className={styles.thumbnail}>
										<Image media={mediaInfoData as IMediaProps} alt="" />
									</div>
									{/* {!isEdit && ( */}
									{/* <div className={styles.mask} title="Edit this media">
										<Button
										// onClick={() => handleShowDetail()}
										>
											<Icon type="cms" variant="edit" />
										</Button>
									</div> */}
									{/* )} */}
								</div>
							) : (
								<div
									className={styles.empty}
									style={
										{
											'--height': isBuilderMode ? '120px' : '324px',
										} as React.CSSProperties
									}
									title="Browse file(s)"
								>
									<Icon type="cms" variant="image" />
									<p>
										Drop your file(s) here or{' '}
										<Button onClick={() => handleAction('add')}>browse</Button>
									</p>
									<small>Max. File Size: 20MB</small>
								</div>
							)}
							{Array.isArray(mediaList) && mediaList.length > 0 && (
								<div className={styles.items}>
									<button className={styles.items__nav} onClick={handlePrevMedia}>
										<Icon type="cms" variant="chevron-left" />
									</button>
									<div className={styles.items__list}>
										{checkArr(mediaList) &&
											mediaList.map((media, idx) => (
												<button
													key={idx}
													className={cn(
														styles.items__thumb,
														idx === currentMediaIdx ? styles.active : ''
													)}
													onClick={() => setCurrentMediaIdx(idx)}
												>
													<Image media={media as unknown as IMediaProps} alt="" />
												</button>
											))}
									</div>
									<button className={styles.items__nav} onClick={handleNextMedia}>
										<Icon type="cms" variant="chevron-right" />
									</button>
								</div>
							)}
						</div>
						<div className={styles.toolbar}>
							<div className={styles.toolbar__list}>
								{toolbar.map((tool, idx) => (
									<button
										key={idx}
										className={styles.toolbar__button}
										onClick={() => handleAction(tool.action)}
										title={tool.name}
									>
										<Icon type="cms" variant={tool.icon} />
									</button>
								))}
							</div>
						</div>
					</div>
				</>
			)}
			<div className={styles.info__fixed}>
				{Object.entries(fixedInfo).map(([key, value]) => (
					<div key={key} className={styles.info__fixed_item}>
						<span className={styles.info__fixed_label}>{key}</span>
						<span className={styles.info__fixed_value}>{value}</span>
					</div>
				))}
			</div>
			<div className={styles.info__editable}>
				{Object.entries(editableInfo).map(([key, value]) => (
					<div key={key} className={styles.info__editable_item}>
						<label>{key}</label>
						<Input
							type="text"
							className="collect__input has__border"
							name={key}
							value={value || ''}
							placeholder={key}
							onChange={handleOnChange}
						/>
					</div>
				))}
			</div>
			<Button className="collect__button yellow" onClick={handleSave}>
				Save
			</Button>
		</div>
	)
}
