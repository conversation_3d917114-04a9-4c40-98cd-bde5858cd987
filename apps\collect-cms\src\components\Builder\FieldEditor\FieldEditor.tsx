import { Icon } from '@collective/core'
import { usePathname } from 'next/navigation'
import { useMemo } from 'react'
import type { ComponentType } from 'react'
import styles from './field.module.scss'
import * as REGULAR_FIELDS from './regular'
import type {
	TextProps,
	RichTextProps,
	RelationProps,
	BooleanProps,
	MediaProps,
	LongTextProps,
	DateTimeProps,
	SelectionProps,
	ComponentProps,
	ColorPickerProps,
} from './regular'

export interface FieldProps<T> {
	name?: string
	type: string
	required?: boolean
	value?: T
	size?: number
	layerPos?: string
	placeholder?: string
	customField?: string
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	onChange?: (props: { field: string; value: any }) => void
}

interface FieldPropsMap<T> {
	Text: FieldProps<T> & TextProps<T>
	LongText: FieldProps<T> & LongTextProps<T>
	RichText: FieldProps<T> & RichTextProps<T>
	DateTime: FieldProps<T> & DateTimeProps<T>
	Relation: FieldProps<T> & RelationProps<T>
	Boolean: FieldProps<T> & BooleanProps<T>
	Media: FieldProps<T> & MediaProps<T>
	Selection: FieldProps<T> & SelectionProps<T>
	ColorPicker: FieldProps<T> & ColorPickerProps<T>
	Component: FieldProps<T> & ComponentProps<T>
}

export const FieldEditor = <T,>(props: FieldProps<T>) => {
	const pathname = usePathname()
	const propsType = props.type ?? ''
	const propsCustomField = props.customField ?? '' // this type is higher priority than propsType
	const onChange = props.onChange
	// propsCustomField !== '' && propsType !== 'richtext' && console.log(props, propsCustomField)

	const fieldByType = useMemo(() => {
		if (propsCustomField === 'plugin::color-picker.color') {
			return 'ColorPicker'
		}

		if (propsCustomField === 'plugin::multi-select.multi-select') {
			return 'Selection'
		}

		switch (propsType) {
			case 'boolean':
				return 'Boolean'
			case 'component':
				return 'Component'
			case 'media':
				return 'Media'
			case 'richtext':
			case 'json':
				return 'RichText'
			// case 'blocks':
			// 	return null
			case 'relation':
				return 'Relation'
			case 'date':
			case 'time':
			case 'datetime':
				return 'DateTime'
			case 'text':
				return 'LongText'
			case 'enumeration':
				return 'Selection'
			default:
				return 'Text'
		}
	}, [propsType, propsCustomField])

	const Field = REGULAR_FIELDS[fieldByType] as ComponentType<
		FieldPropsMap<T>[keyof FieldPropsMap<T>]
	>

	const handleChange = (props: Parameters<FieldPropsMap<T>[typeof fieldByType]['onChange']>[0]) => {
		if (!onChange) return
		// console.log(
		// 	`[FieldEditor] handleChange called for field "${props.field}" with value:`,
		// 	props.value
		// )
		// console.log(`[FieldEditor] Field type: ${fieldByType}, Current data:`, data)

		// setData((prevData) => {
		// 	const newData = { ...prevData }
		// 	const { field, value } = props
		// 	newData.data = {
		// 		...newData.data,
		// 		[field]: value,
		// 	}
		// 	// console.log(`[FieldEditor] New data after update:`, newData)
		// 	return newData
		// })

		onChange(props)
	}

	// Log the props being passed to the field component
	// propsType === 'component' &&
	// 	console.log(`[FieldEditor] Rendering ${fieldByType} with props:`, {
	// 		name: props.name,
	// 		type: props.type,
	// 		value: props.value,
	// 		valueType: typeof props.value,
	// 		required: props.required,
	// 		customField: props.customField,
	// 	})

	// Determine size based on pathname and props
	const fieldSize = useMemo(() => {
		// If pathname starts with 'content-builder/', always use size 12
		if (pathname?.startsWith('/content-builder/')) {
			return 12
		}
		// Otherwise use the default logic
		return props.type === 'media' ? 12 : props.size || 6
		// return props.size
	}, [pathname, props.type, props.size])

	return (
		<div className={styles.wrapper} style={{ '--size': fieldSize } as React.CSSProperties}>
			<div className={styles.title}>
				<p className="collect__subheader">
					{props.name}
					{props.required && <span className={styles.red}>*</span>}
				</p>
				<span title={props.name}>
					<Icon className={styles.icon} type="cms" variant="info" />
				</span>
			</div>
			<Field {...props} onChange={handleChange as never} />
		</div>
	)
}
